from typing import Dict, List, Any, Optional
import re
from loguru import logger
from telegram_notifier import TelegramNotifier
from scoring_system import ScoringSystem # Zaman dilimi etiketi için
from datetime import datetime
from utils import format_price_standard

class NotificationService:
    """Tüm bildirimleri yöneten servis sınıfı"""
    
    def __init__(self, config: Dict[str, Any], stats_tracker=None):
        """
        Bildirim servisini başlatır

        Args:
            config: Uygulama yapılandırması
            stats_tracker: StatsTracker instance (aktif sinyal kontrolü için)
        """
        self.config = config
        self.stats_tracker = stats_tracker  # Aktif sinyal kontrolü için
        self.telegram_enabled = config.get("telegram_enabled", False)
        self.telegram: Optional[TelegramNotifier] = None
        self.scoring_system = ScoringSystem() # Etiket için geçici örnek
        self.stats_reporter = None  # StatsReporter referansı (sonradan atanacak)
        # Telegram'a en son gönderilen rapor metinlerini saklamak için
        self.last_sent_reports: Dict[str, str] = {}

        # Telegram etkinse başlat
        if self.telegram_enabled:
            bot_token = config.get("telegram_bot_token", "")
            chat_id = config.get("telegram_chat_id", "")

            if bot_token and chat_id:
                try:
                    self.telegram = TelegramNotifier(bot_token, chat_id)
                    logger.info("Telegram bildirimleri etkinleştirildi")
                except Exception as e:
                     logger.error(f"TelegramNotifier başlatılırken hata oluştu: {e}")
                     self.telegram_enabled = False # Hata varsa devre dışı bırak
            else:
                logger.warning("Telegram bildirimleri etkin ancak token veya chat_id eksik!")
                self.telegram_enabled = False # Eksik bilgi varsa devre dışı bırak
        else:
            logger.info("Telegram bildirimleri devre dışı")

    def notify_pattern(self, symbol: str, timeframe: str,
                         pattern_data: Dict[str, Any],
                         price: Optional[float],
                         score: Optional[float] = None) -> bool:
        """Pattern bildirimi gönderir"""
        if not self.telegram_enabled or not self.telegram:
            logger.debug("Telegram devre dışı veya başlatılamadı, pattern bildirimi atlanıyor.")
            return False

        # Zaman dilimi etiketini al
        tf_label = self.scoring_system._get_timeframe_label(timeframe)

        # Sadece ana zaman dilimi (4s) için pattern bildirimi gönder
        if timeframe != self.scoring_system.main_timeframe:
             logger.debug(f"{symbol} {tf_label} için pattern bildirimi atlanıyor (ana zaman dilimi değil).")
             return False

        return self.telegram.send_pattern_alert(
            symbol, tf_label, pattern_data, price, score
        )

    def notify_score_report(self, symbol_reports: Dict[str, str]) -> bool:
        """Puanlama raporunu, sadece değişen veya yeni semboller için gönderir."""
        if not self.telegram_enabled or not self.telegram:
            logger.debug("Telegram devre dışı veya başlatılamadı, puanlama raporu atlanıyor.")
            return False

        all_sent_successfully = True
        sent_count = 0
        processed_symbols = set()

        if not symbol_reports:
             logger.info("Gönderilecek puanlama raporu bulunamadı.")
             # Eski gönderilmiş raporları temizle (artık hiç sinyal yoksa)
             self.last_sent_reports = {}
             return True        # Sembolleri skorlarına göre sıralayarak göndermek daha mantıklı olabilir
        try:
            sorted_symbols = sorted(symbol_reports.keys(), key=lambda sym: float(symbol_reports[sym].split("Net Skor: *")[1].split("*")[0]), reverse=True)
        except Exception:
            # Skor parse edilemezse alfabetik sırala
            sorted_symbols = sorted(symbol_reports.keys())

        for symbol in sorted_symbols:
            current_report_text = symbol_reports.get(symbol)
            if not current_report_text: # Boş raporları atla (gerçi yukarıda kontrol var)
                continue

            # ÖNCE: Bu sembol için aktif sinyal (TP1/TP2) kontrolü yap
            if self.stats_tracker and hasattr(self.stats_tracker, 'has_active_signal_for_symbol'):
                if self.stats_tracker.has_active_signal_for_symbol(symbol):
                    logger.debug(f"⚠️ {symbol} için aktif sinyal mevcut, rapor gönderilmiyor (TP1/TP2 durumu olabilir)")
                    continue

            processed_symbols.add(symbol) # Bu sembolü işlendi olarak işaretle
            last_sent_text = self.last_sent_reports.get(symbol)

            # --- Gönderme Koşulu ---
            should_send = False

            # Rapor içeriğinden yön bilgisini çıkar (bull/bear)
            current_direction = None
            if "⬆️ LONG" in current_report_text:
                current_direction = "bull"
            elif "⬇️ SHORT" in current_report_text:
                current_direction = "bear"

            # Son gönderilen rapordan yön bilgisini çıkar
            last_direction = None
            if last_sent_text:
                if "⬆️ LONG" in last_sent_text:
                    last_direction = "bull"
                elif "⬇️ SHORT" in last_sent_text:
                    last_direction = "bear"

            # Rapor içeriğinden pattern bilgisini çıkar
            current_pattern = None
            base_signals_start = current_report_text.find("🔍 Base Signals:")
            if base_signals_start != -1:
                base_signals_end = current_report_text.find("\n\n", base_signals_start)
                if base_signals_end != -1:
                    base_signals_text = current_report_text[base_signals_start:base_signals_end]
                    # Pattern adını çıkar (örn. PLH1, TRIB)
                    import re
                    pattern_match = re.search(r'- ([A-Z0-9]+)', base_signals_text)
                    if pattern_match:
                        current_pattern = pattern_match.group(1)

            # Son gönderilen rapordan pattern bilgisini çıkar
            last_pattern = None
            if last_sent_text:
                base_signals_start = last_sent_text.find("🔍 Base Signals:")
                if base_signals_start != -1:
                    base_signals_end = last_sent_text.find("\n\n", base_signals_start)
                    if base_signals_end != -1:
                        base_signals_text = last_sent_text[base_signals_start:base_signals_end]
                        # Pattern adını çıkar (örn. PLH1, TRIB)
                        import re
                        pattern_match = re.search(r'- ([A-Z0-9]+)', base_signals_text)
                        if pattern_match:
                            last_pattern = pattern_match.group(1)

            if last_sent_text is None: # İlk kez raporlanıyorsa
                should_send = True
                logger.info(f"Sembol '{symbol}' ilk kez raporlanıyor, gönderilecek.")
            elif current_direction != last_direction: # Yön değiştiyse (bull -> bear veya bear -> bull)
                should_send = True
                logger.info(f"Sembol '{symbol}' yönü değişti ({last_direction} -> {current_direction}), yeni rapor gönderilecek.")
            elif current_pattern != last_pattern: # Pattern değiştiyse (TRIB -> PLH1 gibi)
                should_send = True
                logger.info(f"Sembol '{symbol}' pattern değişti ({last_pattern} -> {current_pattern}), yeni rapor gönderilecek.")
            else:
                logger.debug(f"Sembol '{symbol}' yönü ve pattern değişmediği için ({current_direction}, {current_pattern}) gönderim atlanıyor.")

            if should_send:
                sent_count += 1
                # Rapor içeriğini zenginleştir - Volatilite ve Fibonacci bilgilerini ekle
                if "Volatilite:" not in current_report_text and "volatility_level" in symbol_reports.get(f"{symbol}_data", {}):
                    volatility_level = symbol_reports.get(f"{symbol}_data", {}).get("volatility_level", "unknown")
                    if volatility_level != "unknown":
                        volatility_info = f"\n🌊 Volatilite: {volatility_level.upper()}"
                        # Trade Levels bölümünden sonra ekle
                        trade_levels_end = current_report_text.find("\n\n", current_report_text.find("💹 Trade Levels:"))
                        if trade_levels_end != -1:
                            current_report_text = current_report_text[:trade_levels_end] + volatility_info + current_report_text[trade_levels_end:]

                # Fibonacci seviyesi bilgisini ekle
                if "Fib Seviyesi:" not in current_report_text and "fib_level" in symbol_reports.get(f"{symbol}_data", {}):
                    fib_level = symbol_reports.get(f"{symbol}_data", {}).get("fib_level")
                    if fib_level:
                        fib_info = f"\n📏 Fib Seviyesi: {fib_level}"
                        # Trade Levels bölümünden sonra ekle
                        trade_levels_end = current_report_text.find("\n\n", current_report_text.find("💹 Trade Levels:"))
                        if trade_levels_end != -1:
                            current_report_text = current_report_text[:trade_levels_end] + fib_info + current_report_text[trade_levels_end:]

                formatted_report = f"```\n{current_report_text}\n```"
                if self.telegram.send_message(formatted_report, parse_mode=None):
                    # Başarıyla gönderildiyse saklanan raporu güncelle
                    self.last_sent_reports[symbol] = current_report_text
                    logger.debug(f"Sembol '{symbol}' raporu başarıyla gönderildi ve saklandı.")
                else:
                    all_sent_successfully = False
                    logger.error(f"Sembol '{symbol}' için puanlama raporu gönderilemedi.")
                    # Başarısız olursa, bir sonraki döngüde tekrar göndermeyi denemek için
                    # saklanan raporu GÜNCELLEME.
                    # Veya eski raporu silip ilk kez gönderiliyormuş gibi davranabiliriz:
                    # if symbol in self.last_sent_reports: del self.last_sent_reports[symbol]

        # Güncel raporda artık olmayan eski sembolleri hafızadan sil
        symbols_to_remove = set(self.last_sent_reports.keys()) - processed_symbols
        if symbols_to_remove:
            logger.info(f"Artık raporda olmayan eski semboller hafızadan siliniyor: {symbols_to_remove}")
            for symbol in symbols_to_remove:
                del self.last_sent_reports[symbol]

        if sent_count > 0:
            logger.info(f"Toplam {sent_count} sembol için rapor Telegram'a gönderildi/güncellendi.")
        else:
             logger.info("Bu döngüde Telegram'a gönderilecek yeni/güncellenmiş rapor bulunamadı.")

        return all_sent_successfully

    def send_startup_message(self) -> bool:
        """Uygulama başlangıç mesajı gönderir"""
        if not self.telegram_enabled or not self.telegram:
            return False

        message = f"📱 *AUTOMATON v1.3* 📈\n\n"
        message += f"✓ Semboller: `{', '.join(self.config.get('symbols', []))}`\n"
        message += f"✓ Zaman Dilimleri: `{', '.join(self.config.get('timeframes', []))}`\n"
        message += f"✓ Analiz Aralığı: `{self.config.get('analysis_interval', 'N/A')}s`\n"
        message += f"\n_Sistem aktif..._"
        return self.send_message(message)

    def send_error_message(self, error_message: str, symbol: Optional[str] = None, timeframe: Optional[str] = None) -> bool:
        """Hata mesajı gönderir"""
        if not self.telegram_enabled or not self.telegram:
            return False

        header = "❌ *Hata Oluştu!* ❌\n\n"
        context = f" Context: `{symbol}/{timeframe}`\n" if symbol and timeframe else ""
        full_message = header + context + f"```{error_message}```"
        return self.send_message(full_message)

    def send_message(self, message: str, parse_mode: Optional[str] = "Markdown") -> bool:
        """Genel mesaj gönderir"""
        if not self.telegram_enabled or not self.telegram:
            logger.debug("Telegram devre dışı veya başlatılamadı, mesaj atlanıyor.")
            return False

        # parse_mode argümanını telegram_notifier'a aktar
        return self.telegram.send_message(message, parse_mode=parse_mode)

    def format_trade_report(self, symbol: str, score_data: Dict[str, Any]) -> str:
        """
        İşlem raporunu formatlar.

        Args:
            symbol: Kripto para sembolü
            score_data: Puanlama verileri

        Returns:
            str: Formatlanmış rapor metni
        """
        try:
            # Temel bilgileri al
            direction = score_data.get("direction", "unknown")
            base_score = score_data.get("base_score", 0.0)
            confirmation_score = score_data.get("confirmation_score", 0.0)
            negative_score = score_data.get("negative_score", 0.0)
            net_score = score_data.get("net_score", 0.0)

            # Yön emojisi ve yön metni (BULL -> LONG, BEAR -> SHORT)
            direction_emoji = "⬆️" if direction == "bull" else "⬇️" if direction == "bear" else "❓"
            direction_text = "LONG" if direction == "bull" else "SHORT" if direction == "bear" else "UNKNOWN"

            # İşlem seviyeleri
            entry = score_data.get("entry_price")
            sl = score_data.get("sl_price")
            tp = score_data.get("tp_price")
            sl_pct = score_data.get("sl_pct")
            tp_pct = score_data.get("tp_pct")

            # TP1, TP1.5, TP2, TP3 fiyatlarını al
            tp1 = tp  # tp1 = tp (geriye uyumluluk için)
            tp1_5 = score_data.get("tp1_5")  # YENİ
            tp2 = score_data.get("tp2_price")
            tp3 = score_data.get("tp3_price")

            # Volatilite seviyesi ve Fibonacci seviyesini al
            volatility_level = score_data.get("volatility_level", "unknown")
            fib_level = score_data.get("fib_level")

            # Risk-Reward oranı
            rr_ratio = 0
            if tp_pct is not None and sl_pct is not None and sl_pct != 0:
                rr_ratio = abs(tp_pct / sl_pct)

            # Rapor metni oluştur - Yeni format
            timeframe = score_data.get("timeframe", "240")  # Varsayılan olarak 4H
            # Zaman dilimini 240 -> 4h, 60 -> 1h, 720 -> 12h şeklinde dönüştür
            if timeframe == "240":
                tf_display = "4h"
            elif timeframe == "60":
                tf_display = "1h"
            elif timeframe == "720":
                tf_display = "12h"
            elif timeframe == "D":
                tf_display = "D"
            else:
                tf_display = timeframe

            # Sıralamayı değiştir: sembol | zaman dilimi | net skor | yön
            report = f"🎯 {symbol} | {tf_display} | 📊 Net Score: {net_score:.1f} | {direction_emoji} {direction_text} |\n"
            report += " ---------------------------------\n"

            # Puanları ekle (yeni format)
            report += f"📊 Base: {base_score:.1f}, \n      Conf. {confirmation_score:.1f}, \n      Neg. {negative_score:.1f}\n\n"

            # İşlem seviyeleri
            if entry and sl and tp and sl_pct is not None and tp_pct is not None:
                # Kullanılan strateji bilgisini al
                strategy_used = score_data.get("strategy_used", "default")

                report += f"💰 Trade Levels: R:R: {rr_ratio:.2f}\n"
                report += "----------------------------\n"

                # Strateji bilgisini ekle
                if strategy_used:
                    if "fvg_fibonacci" in strategy_used.lower():
                        # FVG + Fibonacci kombinasyonu
                        report += f"      FVG+Fib\n"
                    elif "fibonacci" in strategy_used.lower():
                        # Sadece Fibonacci
                        if strategy_used == "fibonacci_distant":
                            report += f"      Fib (⚠️ Uzak)\n"
                        else:
                            report += f"      Fib\n"

                # Volatilite seviyesini ekle
                if volatility_level != "unknown":
                    report += f"      Volatilite: {volatility_level.upper()}\n"

                # Fibonacci seviyesini ekle
                if fib_level:
                    report += f"      Fib Seviyesi: {fib_level}\n"

                # FVG detaylarını ekle (eğer FVG+Fibonacci stratejisi kullanılıyorsa)
                if strategy_used and "fvg_fibonacci" in strategy_used.lower():
                    fvg_eq = score_data.get("fvg_eq")
                    fvg_top = score_data.get("fvg_top")
                    fvg_bottom = score_data.get("fvg_bottom")
                    strategy_details = score_data.get("strategy_details")

                    if fvg_eq:
                        report += f"      FVG EQ: {format_price_standard(fvg_eq)}\n"
                    if strategy_details:
                        report += f"      Detay: {strategy_details}\n"

                # Entry bilgisi
                report += f"      Entry: {format_price_standard(entry)} \n"

                # Stop Loss bilgisi
                report += f"      SL: {format_price_standard(sl)} ({sl_pct:.2f}%) \n"

                # TP1, TP1.5, TP2, TP3 bilgilerini ekle
                # TP1 bilgisi
                if tp1 and entry:
                    if tp_pct is not None:
                        report += f"      TP1: {format_price_standard(tp1)} ({tp_pct:.2f}%) \n"
                    else:
                        report += f"      TP1: {format_price_standard(tp1)} \n"

                # TP1.5 bilgisi (varsa) YENİ BLOK
                if tp1_5 and entry:
                    try:
                        tp1_5_pct = ((tp1_5 / entry) - 1) * 100 if direction == "bull" else ((entry / tp1_5) - 1) * 100
                        report += f"      TP1.5: {format_price_standard(tp1_5)} ({tp1_5_pct:.2f}%) \n"
                    except (TypeError, ZeroDivisionError):
                        report += f"      TP1.5: {format_price_standard(tp1_5)} \n"

                # TP2 bilgisi (varsa)
                if tp2:
                    tp2_pct = ((tp2 / entry) - 1) * 100 if direction == "bull" else ((entry / tp2) - 1) * 100
                    report += f"      TP2: {format_price_standard(tp2)} ({tp2_pct:.2f}%) \n"

                # TP3 bilgisi (varsa)
                if tp3:
                    tp3_pct = ((tp3 / entry) - 1) * 100 if direction == "bull" else ((entry / tp3) - 1) * 100
                    report += f"      TP3: {format_price_standard(tp3)} ({tp3_pct:.2f}%) \n"

            # Base detayları
            base_details = score_data.get("base_details", [])
            if base_details:
                report += "\n🔍 Base Signals: \n"
                report += "----------------------------\n"
                report += "     - "
                for i, (_, signal_name, _) in enumerate(base_details):
                    if i > 0:
                        report += ", "
                    report += f"{signal_name}"
                report += " -\n\n"

            # Premium/Discount bilgisini ekle
            pd_label = score_data.get("pd_label", "N/A")
            # pd_label zaten EQ bilgisini içeriyorsa, tekrar ekleme
            if "EQ:" in pd_label:
                report += f"📍 {pd_label} \n"
            else:
                eq_level = score_data.get('eq_level', 'N/A')
                # EQ değerini format_price_standard ile formatla
                if eq_level != 'N/A' and isinstance(eq_level, (int, float)):
                    eq_level_formatted = format_price_standard(eq_level)
                    report += f"📍 {pd_label} (EQ: {eq_level_formatted}) \n"
                else:
                    report += f"📍 {pd_label} (EQ: {eq_level}) \n"

            # OB bilgilerini ekle
            bull_ob = "N/A"
            bear_ob = "N/A"
            if "bull_ob" in score_data:
                bull_ob = score_data["bull_ob"]
            if "bear_ob" in score_data:
                bear_ob = score_data["bear_ob"]

            report += "----------------------------\n"
            report += f"    OBs: Bull: {bull_ob}, \n    Bear: {bear_ob}\n"
            report += " ---------------------------"

            # Confirmation detayları
            confirmation_details = score_data.get("confirmation_details", [])
            if confirmation_details:
                report += "\n✅ Confirmations:\n"
                for detail in confirmation_details:
                    report += f"  {detail}\n"

            # Negative detayları
            negative_details = score_data.get("negative_details", [])
            if negative_details:
                report += "\n⛔ Conflicts:\n"
                for detail in negative_details:
                    report += f"  {detail}\n"

            return report

        except Exception as e:
            logger.error(f"İşlem raporu formatlanırken hata: {e}")
            return f"⚠️ {symbol} için rapor oluşturulurken hata: {e}"

    def send_bos_report(self, bos_results: Dict[str, Dict[str, Any]] = None) -> bool:
        """
        BOS (Break of Structure) özetini Telegram'a gönderir.

        Args:
            bos_results: BOS analiz sonuçları sözlüğü (None ise logs/bos_summary.log'dan okur)

        Returns:
            bool: Başarıyla gönderildiyse True
        """
        if not self.telegram_enabled or not self.telegram:
            logger.warning("Telegram bildirimleri devre dışı, BOS raporu gönderilmedi.")
            return False

        # BOS raporu için mesaj
        message = "🔄 *BREAK OF STRUCTURE RAPORU* 🔄\n\n"

        try:
            if bos_results is None:
                # BOS sonuçları sağlanmadı
                logger.warning("BOS sonuçları sağlanmadı, rapor gönderilemedi")
                return False
            else:
                # BOS sonuçlarını direkt olarak formatlayıp gönder
                bullish_count = sum(1 for result in bos_results.values() if result.get('bullish'))
                bearish_count = sum(1 for result in bos_results.values() if result.get('bearish'))

                message += f"*Toplam Bullish BOS:* `{bullish_count}`\n"
                message += f"*Toplam Bearish BOS:* `{bearish_count}`\n\n"

                # Detayları ekle
                if bullish_count > 0:
                    message += "*🟢 Bullish BOS:*\n"
                    for key, result in bos_results.items():
                        if result.get('bullish'):
                            parts = key.split('_')
                            if len(parts) >= 2:
                                symbol = parts[0]
                                timeframe = parts[1]
                                bos_data = result['bullish']
                                price = bos_data.get('price', 0)
                                pct_change = bos_data.get('percent_change', 0)
                                time_str = bos_data.get('timestamp').strftime('%d-%m %H:%M') if bos_data.get('timestamp') else 'N/A'
                                message += f"  {symbol} | {timeframe} | Fiyat: {price} ({pct_change:+.1f}%) | {time_str}\n"
                    message += "\n"

                if bearish_count > 0:
                    message += "*🔴 Bearish BOS:*\n"
                    for key, result in bos_results.items():
                        if result.get('bearish'):
                            parts = key.split('_')
                            if len(parts) >= 2:
                                symbol = parts[0]
                                timeframe = parts[1]
                                bos_data = result['bearish']
                                price = bos_data.get('price', 0)
                                pct_change = bos_data.get('percent_change', 0)
                                time_str = bos_data.get('timestamp').strftime('%d-%m %H:%M') if bos_data.get('timestamp') else 'N/A'
                                message += f"  {symbol} | {timeframe} | Fiyat: {price} ({pct_change:+.1f}%) | {time_str}\n"

                if bullish_count == 0 and bearish_count == 0:
                    message += "*BOS bulunamadı.*"

            # Telegram'a gönder
            success = self.telegram.send_message(message)
            if success:
                logger.info("BOS özet raporu Telegram'a gönderildi.")
            else:
                logger.error("BOS özet raporu Telegram'a gönderilemedi.")
            return success

        except Exception as e:
            logger.error(f"BOS raporu oluşturulurken hata: {e}", exc_info=True)
            return False

    def notify_trade_closure(self, trade_data: Dict[str, Any]) -> bool:
        """
        Kapanan bir işlem için formatlı bir Telegram bildirimi gönderir.

        Args:
            trade_data (Dict[str, Any]): Kapanan işleme ait tüm verileri içeren sözlük.

        Returns:
            bool: Mesaj başarıyla gönderildiyse True.
        """
        if not self.telegram_enabled or not self.telegram:
            return False

        try:
            symbol = trade_data.get("symbol", "N/A")
            direction = trade_data.get("direction", "N/A").upper()
            status = trade_data.get("status", "N/A")
            profit = trade_data.get("profit_percentage", 0.0)
            entry_time_iso = trade_data.get("entry_time")
            result_time_iso = trade_data.get("result_time")            # Yön ve Durum için emojiler ve metinler
            direction_text = "SHORT" if direction == "BEAR" else "LONG"
            profit_emoji = "✅" if profit > 0 else "❌" if profit < 0 else "ℹ️"
            header_text = f"{profit_emoji} *İŞLEM KAPANDI* {profit_emoji}"
            
            # Durum metnini daha anlaşılır hale getir
            if status == "INVALIDATED_STRUCTURE":
                status_text = "İPTAL EDİLDİ (Piyasa Yapısı Değişti)"
            elif status == "PIVOT_SUCCESS" or status == "CANCELLED_PIVOT":
                status_text = "İPTAL EDİLDİ (Yeni Pivot Oluşumu)"
            elif status == "TIMEOUT":
                status_text = "İPTAL EDİLDİ (Zaman Aşımı)"
            else:
                status_text = status # SL, TP1, vs.

            # Zamanları formatla
            try:
                entry_time = datetime.fromisoformat(entry_time_iso).strftime('%d-%m %H:%M')
            except:
                entry_time = "N/A"
            try:
                result_time = datetime.fromisoformat(result_time_iso).strftime('%d-%m %H:%M')
            except:
                result_time = "N/A"

            # Mesajı oluştur
            message = f"{header_text}\n\n"
            message += f"*Sembol:* `{symbol}`\n"
            message += f"*Yön:* `{direction_text}`\n"
            message += f"*Kapanış Nedeni:* `{status_text}`\n"
            message += f"*Kar/Zarar:* `%{profit:.2f}`\n\n"
            message += f"*Giriş Zamanı:* `{entry_time}`\n"
            message += f"*Kapanış Zamanı:* `{result_time}`"

            return self.send_message(message)

        except Exception as e:
            logger.error(f"İşlem kapanış bildirimi oluşturulurken hata: {e}", exc_info=True)
            return False
        except Exception as e:
            logger.error(f"İşlem kapanış bildirimi oluşturulurken hata: {e}", exc_info=True)
            return False

    def notify_new_signal(self, score_data: Dict[str, Any]) -> bool:
        """Yeni bir sinyal oluştuğunda formatlanmış ana raporu gönderir."""
        if not self.telegram_enabled or not self.telegram:
            return False
        
        symbol = score_data.get("symbol")
        if not symbol:
            logger.error("notify_new_signal çağrıldı ancak score_data'da sembol bulunamadı.")
            return False

        logger.info(f"✅ Yeni sinyal için bildirim hazırlanıyor: {symbol}")
        
        # generate_score_report'un beklediği doğru veri yapısını oluşturalım.
        # Bu fonksiyon, {symbol -> {timeframe -> {score_result: ...}}} yapısını bekler.
        main_tf = self.scoring_system.main_timeframe
        data_for_report = {
            symbol: {
                main_tf: {
                    "score_result": score_data
                }
            }
        }
        
        # scoring_system'deki rapor formatlama fonksiyonunu kullanarak rapor metnini al
        report_text_dict = self.scoring_system.generate_score_report(
            all_timeframe_data=data_for_report,
            min_score_threshold=0  # Skoru ne olursa olsun raporla
        )
        
        report_text = report_text_dict.get(symbol)
        
        if not report_text:
             logger.error(f"[{symbol}] için yeni sinyal raporu oluşturulamadı (generate_score_report boş sonuç döndü).")
             return False

        # Başarılı olursa, formatlayıp mesajı gönder
        # "```" kullanarak kod bloğu formatında gönderiyoruz
        return self.send_message(f"```\n{report_text}\n```", parse_mode=None)

    def notify_status_update(self, signal_data: Dict[str, Any], new_status: str) -> bool:
        """Bir işlemin ara durumu değiştiğinde (örn: TP1 oldu) bildirim gönderir."""
        if not self.telegram_enabled or not self.telegram:
            return False
            
        symbol = signal_data.get("symbol", "N/A")
        direction_emoji = "🔼" if signal_data.get("direction") == "BULL" else "🔻"
        message = ""

        if new_status == "ACTIVE":
            message = f"✅ {direction_emoji} *{symbol}* pozisyona giriş yaptı."
        elif new_status == "TP1_HIT":
            message = f"💰 {direction_emoji} *{symbol}* TP1 hedefine ulaştı. Stop seviyesi girişe çekildi."
        elif new_status == "TP1.5_HIT":
            message = f"💰 {direction_emoji} *{symbol}* TP1.5 hedefine ulaştı. Stop seviyesi TP1'e çekildi."
        elif new_status == "TP2_HIT":
            message = f"💰💰 {direction_emoji} *{symbol}* TP2 hedefine ulaştı. Kâr koruma aktif."
        elif new_status == "TP3_HIT":
            message = f"💰💰💰 {direction_emoji} *{symbol}* TP3 hedefine ulaştı. İşlem tamamlandı!"
        
        if message:
            logger.info(f"Durum güncelleme bildirimi gönderiliyor: {message}")
            return self.telegram.send_message(message, parse_mode="Markdown")
        
        return False


